import { gql } from "graphql-tag";

export const GET_LIST_OPPORTUNITY_QUERY = gql`
  query GetListOpportunity(
    $partyId: String!
    $performerId: String!
    $getOpportunityRequest: GetOpportunityRequest!
  ) {
    getListOpportunity(
      partyId: $partyId
      performerId: $performerId
      getOpportunityRequest: $getOpportunityRequest
    ) {
      total
      data {
        goal
        campaignId
        valueReal
        valueExpect
        successRate
        referName
        referPhone
        referEmail
        id
        createdBy
        workEffortTypeId
        partyId
        name
        description
        parentId
        status
        stmId
        createdStamp
        updatedStamp
        endDateExpect
        priorityName
        targetId
        targetType
        targetUrl
        extSource
        connectorId
        processResult
        processPipeline {
          id
          name
        }
      }
    }
  }
`;

export const GET_LIST_TODO = gql`
  query GetListTodo($partyId: String!, $workEffortId: [String]!) {
    getListTodo(partyId: $partyId, workEffortId: $workEffortId) {
      workEffortId
      toDoList {
        listAttachment {
          id
          createdStamp
          createdBy
          updatedBy
          updatedStamp
          partyId
          path
          srcId
          srcName
          srcPath
          srcConfigPathId
          name
          fileType
          type
          status
          referId
        }
        isDone
        id
        workEffortTypeId
        workEffortType {
          id
          name
          group
          createdStamp
          updatedStamp
          updatedBy
          createdBy
          partyId
          actionLinkId
          partyGroupIds
          description
          workFlow {
            stages {
              id
              name
              mode
              workEffortTypeId
            }
          }
        }
        partyId
        name
        description
        parentId
        parentType
        status
        createdStamp
        updatedStamp
        source
        mode
        connectorId
        actionLink {
          name
          uri
          type
          partyId
          fromCollection
          toCollection
          group
          params
          id
          createdStamp
          updatedStamp
          updatedBy
          createdBy
        }
        partyGroupIds
        tagIds
        processResult
      }
    }
  }
`;

export const GET_LIST_WORK_EFFORT_TYPE = gql`
  query GetListWorkEffortType($partyId: String!, $id: String!) {
    getListWorkEffortType(
      partyId: $partyId
      getWorkEffortTypeRequest: { id: $id, isPagination: true }
    ) {
      id
      name
      group
      createdStamp
      updatedStamp
      updatedBy
      createdBy
      partyId
      actionLinkId
      partyGroupIds
      description
      workFlow {
        stages {
          id
          name
          mode
          workEffortTypeId
        }
      }
    }
  }
`;

export const GET_LIST_TICKET = gql`
  query GetListTicket(
    $partyId: String!
    $performerId: String!
    $getTicketRequest: GetTicketRequest!
  ) {
    getListTicket(
      partyId: $partyId
      performerId: $performerId
      getTicketRequest: $getTicketRequest
    ) {
      total
      data {
        id
        workEffortTypeId
        partyId
        name
        description
        status
        parentId
        stmId
        createdStamp
        updatedStamp
        endDateExpect
        endDateActual
        priorityName
        targetId
        targetType
        extSource
        connectorId
      }
    }
  }
`;

export const GET_TICKET_BY_ID = gql`
  query GetTicketById($ticketId: String!) {
    getTicketById(ticketId: $ticketId) {
      id
      accountable {
        id
        status
        partyId
        salutation
        firstName
        middleName
        lastName
        fullName
      }
      workEffortTypeId
      workEffortType {
        id
        name
        group
        createdStamp
        updatedStamp
      }
      partyId
      name
      description
      status
      parentId
      createdStamp
      updatedStamp
      endDateExpect
      endDateActual
      priorityName
      targetId
      targetType
      extSource
    }
  }
`;

export const GET_ATTACHMENT_BY_WORK_EFFORT_ID = gql`
  query GetAttachmentByWorkEffortId(
    $partyId: String!
    $workEffortIds: [String]!
  ) {
    getAttachmentByWorkEffortId(
      partyId: $partyId
      workEffortIds: $workEffortIds
    ) {
      id
      createdStamp
      updatedStamp
      updatedBy
      createdBy
      partyId
      path
      srcId
      srcName
      srcPath
      srcConfigPathId
      name
      fileType
      type
      status
      referId
    }
  }
`;

export const GET_LIST_COMMENT = gql`
  query GetListComment(
    $partyId: String!
    $getCommentRequest: GetCommentRequest!
  ) {
    getListComment(partyId: $partyId, getCommentRequest: $getCommentRequest) {
      total
      data {
        id
        createdStamp
        updatedStamp
        updatedBy
        createdBy {
          id
          fullName
        }
        type
        format
        content
        referId
        referSource
        attachId
      }
    }
  }
`;
export const GET_WORK_EFFORTS = gql`
  query GetWorkEfforts(
    $partnerId: String!
    $performerId: String!
    $workEffortTypeId: String
    $source: String!
    $pageNumber: Int!
    $pageSize: Int!
    $sorts: [BaseSort]
    $attributes: JSON
  ) {
    getWorkEfforts(
      partnerId: $partnerId
      performerId: $performerId
      workEffortTypeId: $workEffortTypeId
      source: $source
      pageNumber: $pageNumber
      pageSize: $pageSize
      sorts: $sorts
      attributes: $attributes
    ) {
      total
      data {
        id
        createdStamp
        updatedStamp
        createdBy
        updatedBy
        name
        partyId
        targetId
        targetType
        targetUrl
        description
        status
        parentId
        workEffortTypeId
        stmId
        workflowId
        endDateExpect
        endDateActual
        startDateActual
        startDateExpect
        source
        priorityName
        priorityValue
        connectorId
        owner {
          id
          fullName
          name
          type
          phone
          email
        }
        mode
        partyGroupIds
        tagIds
        processResult
        processStatus
        processPipeline {
          id
          name
        }
        attachments {
          id
          createdStamp
          updatedStamp
          updatedBy
          createdBy
          partyId
          path
          srcId
          srcName
          srcPath
          srcConfigPathId
          name
          fileType
          type
          status
          referId
        }
        subTasks {
          id
          createdStamp
          updatedStamp
          createdBy
          updatedBy
          name
          partyId
          targetId
          targetType
          targetUrl
          description
          status
          parentId
          workEffortTypeId
          stmId
          workflowId
          endDateExpect
          endDateActual
          startDateActual
          startDateExpect
          source
          priorityName
          priorityValue
          connectorId
          mode
          partyGroupIds
          tagIds
          processResult
          processStatus
        }
      }
    }
  }
`;
export const GET_WORK_EFFORT_BY_ID = gql`
  query GetWorkEffortById($id: String!) {
    getWorkEffortById(id: $id) {
      id
      createdStamp
      updatedStamp
      createdBy
      updatedBy
      name
      partyId
      targetId
      targetType
      targetUrl
      description
      status
      parentId
      workEffortTypeId
      stmId
      workflowId
      endDateExpect
      endDateActual
      startDateActual
      startDateExpect
      source
      priorityName
      priorityValue
      connectorId
      mode
      partyGroupIds
      tagIds
      owner {
        id
        fullName
        name
        type
        phone
        email
      }
      processResult
      processStatus
      processPipeline {
        id
        name
      }
      attachments {
        id
        createdStamp
        updatedStamp
        updatedBy
        createdBy
        partyId
        path
        srcId
        srcName
        srcPath
        srcConfigPathId
        name
        fileType
        type
        status
        referId
      }
      subTasks {
        id
        createdStamp
        updatedStamp
        createdBy
        updatedBy
        name
        partyId
        targetId
        targetType
        targetUrl
        description
        status
        parentId
        workEffortTypeId
        stmId
        workflowId
        endDateExpect
        endDateActual
        startDateActual
        startDateExpect
        source
        priorityName
        priorityValue
        connectorId
        mode
        partyGroupIds
        tagIds
        processResult
        attachments {
          id
          createdStamp
          updatedStamp
          updatedBy
          createdBy
          partyId
          path
          srcId
          srcName
          srcPath
          srcConfigPathId
          name
          fileType
          type
          status
          referId
        }
      }
      actionLink {
        name
        uri
        type
        partyId
        fromCollection
        toCollection
        group
        params
        id
        createdStamp
        updatedStamp
        updatedBy
        createdBy
      }
    }
  }
`;
export const GET_CONNECTOR_BY_RESOURCE = gql`
  query GetConnectorByResource(
    $resourceId: String
    $resourceType: String
    $type: String
  ) {
    getConnectorByResource(
      resourceId: $resourceId
      resourceType: $resourceType
      type: $type
    )
  }
`;
export const GET_TAG = gql`
  query GetTags($connectorId: String) {
    getTags(connectorId: $connectorId)
  }
`;
export const SEARCH_TOPIC = gql`
  query SearchTopic(
    $filterTopicRequest: FilterTopicRequest!
    $partnerId: String!
    $pageSize: Int!
    $currentPage: Int!
  ) {
    searchTopic(
      filterTopicRequest: $filterTopicRequest
      partnerId: $partnerId
      pageSize: $pageSize
      currentPage: $currentPage
    ) {
      totalPages
      totalElements
      currentPage
      content {
        id
        status
        name
        communicationChannel
        accountableId
        accountableName
        createdStamp
        threadId
        roomId
        channelId
        channelType
        customer {
          id
          fullName
          name
          type
          phone
          email
        }
      }
    }
  }
`;
export const GET_MY_WORK_EFFORT_TODAY = gql`
  query GetMyWorkEffortToday(
    $partnerId: String!
    $performerId: String!
    $source: String!
  ) {
    getMyWorkEffortToDay(
      partnerId: $partnerId
      performerId: $performerId
      source: $source
    ) {
      id
      createdStamp
      updatedStamp
      createdBy
      updatedBy
      name
      partyId
      targetId
      targetType
      targetUrl
      description
      status
      parentId
      workEffortTypeId
      stmId
      workflowId
      endDateExpect
      endDateActual
      startDateActual
      startDateExpect
      source
      priorityName
      priorityValue
      connectorId
      mode
      partyGroupIds
      tagIds
      processResult
      processStatus
      owner {
        id
        fullName
        name
        type
        phone
        email
      }
    }
  }
`;
export const GET_MESSAGES = gql`
  query GetMessages($topicId: String!, $pageSize: Int, $pageNumber: Int) {
    getMessages(
      topicId: $topicId
      pageSize: $pageSize
      pageNumber: $pageNumber
    ) {
      total
      data {
        id
        createdStamp
        updatedStamp
        updatedBy
        type
        format
        content
        referId
        referSource
        attachId
        createdBy {
          id
          fullName
          name
          type
          phone
          email
        }
        attachmentUrl
      }
    }
  }
`;
export const GET_RESOURCE_RELATED_TOPIC = gql`
query GetResourceRelatedTopic(
    $topicId: String!
    $resourceType: String!
){
    getResourceRelatedTopic(topicId: $topicId, resourceType: $resourceType)
}
`;
