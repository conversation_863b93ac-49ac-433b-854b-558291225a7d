// src/SDK.ts
import { ProductService } from "../lib/product/index";
import { AuthService } from "../lib/auth/index";
import { OrderService } from "../lib/order/index";
import { ServiceManagementService } from "./service/index";
import { UserService } from "../lib/user/index";
import { environmentEndpoints } from "../../config/config";
import { PaymentService } from "../lib/payment/index";
import { CrmService } from "../lib/crm/index";
import { WarehouseService } from "../lib/warehouse/index";
import { ComputingService } from "../lib/computing/index";
import { CampaignService } from "./campaign";
import { ImageService } from "./image";
import { PaymentServiceV2 } from "./paymentV2";
import { WarehouseServiceV2 } from "./warehouseV2";
import { DeepLinkVietQrService } from "./deepLinkVietQr";
import { ComhubService } from "./comhub";
import { PortalService } from "./portal";
import { UploadService } from "./upload";
import { GetImageService } from "./getImage";
import { AccountingService } from "./accounting";
import { OmnigatewayService } from "./omnigateway";
import { AuthorizationService } from "./token";
import { ZcaService } from "./zca";
import { CashbookService } from "./cashbook";
import { StoreService } from "./store";
export interface Endpoints {
  product: string;
  crm: string;
  auth: string;
  order: string;
  user: string;
  payment: string;
  service: string;
  warehouse: string;
  computing: string;
  campaign: string;
  image: string;
  paymentV2: string;
  warehouseV2: string;
  deepLinkVietQr: string;
  comhub: string;
  portal: string;
  upload: string;
  getImage: string;
  accounting: string;
  omnigateway: string;
  authorization: string;
  zca: string;
  cashbook: string;
  store: string;
}

export class SDK {
  public product: ProductService;
  public auth: AuthService;
  public order: OrderService;
  public user: UserService;
  public payment: PaymentService;
  public crm: CrmService;
  public service: ServiceManagementService;
  public warehouse: WarehouseService;
  public computing: ComputingService;
  public campaign: CampaignService;
  public image: ImageService;
  public paymentV2: PaymentServiceV2;
  public warehouseV2: WarehouseServiceV2;
  public deepLinkVietQr: DeepLinkVietQrService;
  public comhub: ComhubService;
  public portal: PortalService;
  public upload: UploadService;
  public getImage: GetImageService;
  public accounting: AccountingService;
  public omnigateway: OmnigatewayService;
  public authorization: AuthorizationService;
  public zca: ZcaService;
  public cashbook: CashbookService;
  public store: StoreService;
  public token: string | null = null;
  constructor(
    public orgId: string,
    public storeId: string,
    public environment: "dev" | "live"
  ) {
    const endpoints: Endpoints = environmentEndpoints[environment];
    this.product = new ProductService(endpoints.product, orgId, storeId);
    this.auth = new AuthService(endpoints.auth, orgId, storeId);
    this.order = new OrderService(endpoints.order, orgId, storeId);
    this.user = new UserService(endpoints.user, orgId, storeId);
    this.payment = new PaymentService(endpoints.payment, orgId, storeId);
    this.crm = new CrmService(endpoints.crm, orgId, storeId);
    this.service = new ServiceManagementService(
      endpoints.service,
      orgId,
      storeId
    );
    this.warehouse = new WarehouseService(endpoints.warehouse, orgId, storeId);
    this.computing = new ComputingService(endpoints.computing, orgId, storeId);
    this.campaign = new CampaignService(endpoints.campaign, orgId, storeId);
    this.image = new ImageService(endpoints.image, orgId, storeId);
    this.paymentV2 = new PaymentServiceV2(endpoints.paymentV2, orgId, storeId);
    this.warehouseV2 = new WarehouseServiceV2(
      endpoints.warehouseV2,
      orgId,
      storeId
    );
    this.deepLinkVietQr = new DeepLinkVietQrService(
      endpoints.deepLinkVietQr,
      orgId,
      storeId
    );
    this.comhub = new ComhubService(endpoints.comhub, orgId, storeId);
    this.portal = new PortalService(endpoints.portal, orgId, storeId);
    this.upload = new UploadService(endpoints.upload, orgId, storeId);
    this.getImage = new GetImageService(endpoints.getImage, orgId, storeId);
    this.accounting = new AccountingService(
      endpoints.accounting,
      orgId,
      storeId
    );
    this.omnigateway = new OmnigatewayService(
      endpoints.omnigateway,
      orgId,
      storeId
    );
    this.authorization = new AuthorizationService(
      endpoints.authorization,
      orgId,
      storeId
    );
    this.zca = new ZcaService(endpoints.zca, orgId, storeId);
    this.cashbook = new CashbookService(endpoints.cashbook, orgId, storeId);
    this.store = new StoreService(endpoints.store, orgId, storeId); // Using product endpoint for now
    // Initialize other services here
  }

  setToken(token: string) {
    this.token = token;
    // Also set the token in each service
    this.product.setToken(token);
    this.auth.setToken(token);
    this.order.setToken(token);
    this.user.setToken(token);
    this.payment.setToken(token);
    this.crm.setToken(token);
    this.service.setToken(token);
    this.warehouse.setToken(token);
    this.computing.setToken(token);
    this.image.setToken(token);
    this.paymentV2.setToken(token);
    this.warehouseV2.setToken(token);
    this.deepLinkVietQr.setToken(token);
    this.campaign.setToken(token);
    this.portal.setToken(token);
    this.upload.setToken(token);
    this.getImage.setToken(token);
    // this.shareZalo.setToken(token)
    // Set token for other services here
    this.accounting.setToken(token);
    this.omnigateway.setToken(token);
    this.authorization.setToken(token);
    this.zca.setToken(token);
    this.cashbook.setToken(token);
    this.store.setToken(token);
  }
  // các module export từ serviceSDK.ts set storeId vào serviceSDK.ts
  // src/service.ts
  setStoreId(storeId: string) {
    this.storeId = storeId;
    this.product.setStoreId(storeId);
    this.auth.setStoreId(storeId);
    this.order.setStoreId(storeId);
    this.user.setStoreId(storeId);
    this.payment.setStoreId(storeId);
    this.crm.setStoreId(storeId);
    this.service.setStoreId(storeId);
    this.warehouse.setStoreId(storeId);
    this.image.setStoreId(storeId);
    this.paymentV2.setStoreId(storeId);
    this.warehouseV2.setStoreId(storeId);
    this.deepLinkVietQr.setStoreId(storeId);
    this.campaign.setStoreId(storeId);
    this.portal.setStoreId(storeId);
    this.upload.setStoreId(storeId);
    this.getImage.setStoreId(storeId);
    this.accounting.setStoreId(storeId);
    this.omnigateway.setStoreId(storeId);
    this.auth.setStoreId(storeId);
    this.zca.setStoreId(storeId);
    this.cashbook.setStoreId(storeId);
    this.store.setStoreId(storeId);
    // Set storeId for other services here
  }
  setOrgId(orgId: string) {
    this.orgId = orgId;
    this.product.setOrgId(orgId);
    this.auth.setOrgId(orgId);
    this.order.setOrgId(orgId);
    this.user.setOrgId(orgId);
    this.payment.setOrgId(orgId);
    this.crm.setOrgId(orgId);
    this.service.setOrgId(orgId);
    this.warehouse.setOrgId(orgId);
    this.image.setOrgId(orgId);
    this.paymentV2.setOrgId(orgId);
    this.warehouseV2.setOrgId(orgId);
    this.deepLinkVietQr.setOrgId(orgId);
    this.campaign.setOrgId(orgId);
    this.portal.setOrgId(orgId);
    this.upload.setOrgId(orgId);
    this.getImage.setOrgId(orgId);
    this.accounting.setOrgId(orgId);
    this.omnigateway.setOrgId(orgId);
    this.auth.setOrgId(orgId);
    this.zca.setOrgId(orgId);
    this.cashbook.setOrgId(orgId);
    this.store.setOrgId(orgId);
  }
}

// Export default for direct imports
export default SDK;
