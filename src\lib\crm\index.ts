import {
  ADD_ATTACHMENT_FOR_WORK_EFFORT,
  ADD_COMMENT,
  ADD_OPPORTUNITY_MUTATION,
  ADD_TICKED,
  UPDATE_STATUS_ATTACHMENT_BY_ID,
  UPDATE_WORK_EFFORT_DESCRIPTION,
  UPDATE_WORK_EFFORT_NAME,
  UPDATE_WORK_EFFORT_STATUS,
  CREATE_WORK_EFFORT,
  UPDATE_WORK_EFFORT_PROCESS_STATUS,
  CREATE_CONNECTOR,
  ADD_TAG,
  UPDATE_CONNECTOR_DESCRIPTION,
  REMOVE_TAG,
  CLOSE_TOPIC,
  CREATE_TOPIC,
  ADD_TOPIC_RELATED_RESOURCE,
} from "../../graphql/crm/mutations";
import {
  GET_ATTACHMENT_BY_WORK_EFFORT_ID,
  GET_LIST_COMMENT,
  GET_LIST_OPPORTUNITY_QUERY,
  GET_LIST_TICKET,
  GET_LIST_TODO,
  GET_LIST_WORK_EFFORT_TYPE,
  GET_TICKET_BY_ID,
  GET_WORK_EFFORTS,
  GET_WORK_EFFORT_BY_ID,
  GET_CONNECTOR_BY_RESOURCE,
  GET_TAG,
  SEARCH_TOPIC,
  GET_MY_WORK_EFFORT_TODAY,
  GET_MESSAGES,
  GET_RESOURCE_RELATED_TOPIC,
} from "../../graphql/crm/queries";
import {
  AddAttachmentRequest,
  AddOpportunityRequest,
  AddTicketRequest,
  GetCommentRequest,
  GetOpportunityRequest,
  getTicketRequest,
  BaseSort,
  AddAttachmentRequestFull,
} from "../../types/crm";
import { Service } from "../serviceSDK";

export class CrmService extends Service {
  constructor(endpoint: string, orgId: string, storeId: string) {
    super(endpoint, orgId, storeId);
  }

  setToken(token: string) {
    this.token = token;
  }

  async addOpportunity(
    performerId: string,
    addOpportunityRequest?: AddOpportunityRequest
  ) {
    const mutation = ADD_OPPORTUNITY_MUTATION;
    const variables = {
      partyId: this.orgId,
      performerId,
      addOpportunityRequest,
    };

    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response.addOpportunity;
    } catch (error) {
      console.log(`Error in addOpportunity: ${error}`);
      throw error;
    }
  }

  async getListOpportunity(
    performerId: string,
    getOpportunityRequest: GetOpportunityRequest
  ) {
    const query = GET_LIST_OPPORTUNITY_QUERY;
    const variables = {
      partyId: this.orgId,
      performerId,
      getOpportunityRequest,
    };

    try {
      const response = await this.graphqlQueryV2(query, variables);
      return response.getListOpportunity;
    } catch (error) {
      console.log(`Error in getListOpportunity: ${error}`);
      throw error;
    }
  }

  async getListTodo(workEffortId: string[]) {
    const query = GET_LIST_TODO;
    const variables = {
      partyId: this.orgId,
      workEffortId,
    };

    try {
      const response = await this.graphqlQueryV2(query, variables);
      return response.getListTodo;
    } catch (error) {
      console.log(`Error in getListTodo: ${error}`);
      throw error;
    }
  }

  async getListWorkEffortType(id: string) {
    const query = GET_LIST_WORK_EFFORT_TYPE;
    const variables = {
      partyId: this.orgId,
      id,
    };

    try {
      const response = await this.graphqlQueryV2(query, variables);
      return response.getListWorkEffortType;
    } catch (error) {
      console.log(`Error in getListWorkEffortType: ${error}`);
      throw error;
    }
  }

  async updateStatusAttachmentById(
    performerId: string,
    attachmentId: string,
    status: string
  ) {
    const mutation = UPDATE_STATUS_ATTACHMENT_BY_ID;
    const variables = {
      performerId,
      attachmentId,
      status,
    };

    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response.updateStatusAttachmentById;
    } catch (error) {
      console.log(`Error in updateStatusAttachmentById: ${error}`);
      throw error;
    }
  }

  async updateWorkEffortDescription(
    performerId: string,
    workEffortId: string,
    description: string
  ) {
    const mutation = UPDATE_WORK_EFFORT_DESCRIPTION;
    const variables = {
      performerId,
      workEffortId,
      description,
    };

    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response.updateWorkEffortDescription;
    } catch (error) {
      console.log(`Error in updateWorkEffortDescription: ${error}`);
      throw error;
    }
  }

  async updateWorkEffortName(
    performerId: string,
    workEffortId: string,
    name: string
  ) {
    const mutation = UPDATE_WORK_EFFORT_NAME;
    const variables = {
      partyId: this.orgId,
      performerId,
      workEffortId,
      newName: name,
    };

    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response.updateWorkEffortName;
    } catch (error) {
      console.log(`Error in updateWorkEffortName: ${error}`);
      throw error;
    }
  }

  async updateWorkEffortStatus(
    performerId: string,
    workEffortId: string,
    source: string,
    status: string
  ) {
    const mutation = UPDATE_WORK_EFFORT_STATUS;
    const variables = {
      partyId: this.orgId,
      performerId,
      workEffortId,
      source,
      status,
    };

    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response.updateWorkEffortStatus;
    } catch (error) {
      console.log(`Error in updateWorkEffortStatus: ${error}`);
      throw error;
    }
  }

  async addAttachmentForWorkEffort(
    performerId: string,
    workEffortId: string,
    addAttachmentRequest: AddAttachmentRequest
  ) {
    const mutation = ADD_ATTACHMENT_FOR_WORK_EFFORT;
    const variables = {
      partyId: this.orgId,
      performerId,
      workEffortId,
      addAttachmentRequest,
    };

    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response.addAttachmentForWorkEffort;
    } catch (error) {
      console.log(`Error in addAttachmentForWorkEffort: ${error}`);
      throw error;
    }
  }

  async getListTicket(performerId: string, getTicketRequest: getTicketRequest) {
    const query = GET_LIST_TICKET;
    const variables = {
      partyId: this.orgId,
      performerId,
      getTicketRequest,
    };

    try {
      const response = await this.graphqlQueryV2(query, variables);
      return response.getListTicket;
    } catch (error) {
      console.log(`Error in getListTicket: ${error}`);
      throw error;
    }
  }

  async addTicket(
    performerId: string,
    addTicketRequest: AddTicketRequest,
    addAttachmentRequest: [AddAttachmentRequest]
  ) {
    const mutation = ADD_TICKED;
    const variables = {
      partyId: this.orgId,
      performerId,
      addTicketRequest,
      addAttachmentRequest,
    };

    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response.addTicket;
    } catch (error) {
      console.log(`Error in addTicket: ${error}`);
      throw error;
    }
  }

  async getTicketById(ticketId: string) {
    const query = GET_TICKET_BY_ID;
    const variables = {
      ticketId,
    };

    try {
      const response = await this.graphqlQueryV2(query, variables);
      return response.getTicketById;
    } catch (error) {
      console.log(`Error in getTicketById: ${error}`);
      throw error;
    }
  }

  async getAttachmentByWorkEffortId(workEffortIds: [string]) {
    const query = GET_ATTACHMENT_BY_WORK_EFFORT_ID;
    const variables = {
      partyId: this.orgId,
      workEffortIds,
    };

    try {
      const response = await this.graphqlQueryV2(query, variables);
      return response.getAttachmentByWorkEffortId;
    } catch (error) {
      console.log(`Error in getAttachmentByWorkEffortId: ${error}`);
      throw error;
    }
  }

  async getListComment(getCommentRequest: GetCommentRequest) {
    const query = GET_LIST_COMMENT;
    const variables = {
      partyId: this.orgId,
      getCommentRequest,
    };

    try {
      const response = await this.graphqlQueryV2(query, variables);
      return response.getListComment;
    } catch (error) {
      console.log(`Error in getListComment: ${error}`);
      throw error;
    }
  }

  async addComment(params: any) {
    const mutation = ADD_COMMENT;
    const variables = {
      partyId: this.orgId,
      performerId: params.performerId,
      format: params.addAttachmentRequest ? "ATTACHMENT" : "TEXT",
      content: params.content,
      referId: params.referId,
      addAttachmentRequest: params.addAttachmentRequest,
    };

    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response.addComment;
    } catch (error) {
      console.log(`Error in addComment: ${error}`);
      throw error;
    }
  }
  async createWorkEffort(
    createdBy: string,
    name: String,
    decription: string,
    workEffortTypeId: string,
    source: string,
    attributes: object,
    addAttachmentRequest: AddAttachmentRequest,
    parentId?: string
  ) {
    const query = CREATE_WORK_EFFORT;
    const variables = {
      partnerId: this.orgId,
      createdBy,
      name,
      decription,
      workEffortTypeId,
      source,
      attributes,
      addAttachmentRequest,
      parentId,
    };
    try {
      const response = await this.graphqlMutationV2(query, variables);
      return response.createWorkEffort;
    } catch (error) {
      throw error;
    }
  }
  async getWorkEfforts(
    performerId: string,
    workEffortTypeId: string,
    source: string,
    pageNumber: number,
    pageSize: number,
    sorts: BaseSort,
    attributes: object
  ) {
    const query = GET_WORK_EFFORTS;
    const variables = {
      partnerId: this.orgId,
      performerId,
      workEffortTypeId,
      source,
      pageNumber,
      pageSize,
      sorts,
      attributes,
    };
    try {
      const response = await this.graphqlQueryV2(query, variables);
      return response.getWorkEfforts;
    } catch (error) {
      throw error;
    }
  }
  async getWorkEffortById(id: string) {
    const query = GET_WORK_EFFORT_BY_ID;
    const variables = {
      id,
    };
    try {
      const response = await this.graphqlMutationV2(query, variables);
      return response.getWorkEffortById;
    } catch (error) {
      throw error;
    }
  }
  async updateWorkEffortProcessStatus(
    workEffortId: string,
    processStatus: string,
    performerId: string
  ) {
    const mutation = UPDATE_WORK_EFFORT_PROCESS_STATUS;
    const variables = {
      workEffortId,
      processStatus,
      performerId,
    };
    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response.updateWorkEffortProcessStatus;
    } catch (error) {
      throw error;
    }
  }
  // tag
  async getConnectorByResource(
    resourceId: string,
    resourceType: string,
    type: string
  ) {
    const query = GET_CONNECTOR_BY_RESOURCE;
    const variables = {
      resourceId,
      resourceType,
      type,
    };
    try {
      const response = await this.graphqlQueryV2(query, variables);
      return response.getConnectorByResource;
    } catch (error) {
      throw error;
    }
  }
  async createConnector(
    resourceId: string,
    resourceType: string,
    description: string,
    type: string,
    createdBy: string
  ) {
    const mutation = CREATE_CONNECTOR;
    const variables = {
      resourceId,
      resourceType,
      description,
      type,
      createdBy,
    };
    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response.createConnector;
    } catch (error) {
      throw error;
    }
  }
  async addTag(
    connectorId: string,
    tagTitle: string,
    tagId: string,
    addedBy: string
  ) {
    const mutation = ADD_TAG;
    const variables = {
      connectorId,
      tagTitle,
      tagId,
      addedBy,
    };
    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response.addTag;
    } catch (error) {
      throw error;
    }
  }
  async updateConnectorDescription(
    connectorId: string,
    description: string,
    updatedBy: string
  ) {
    const mutation = UPDATE_CONNECTOR_DESCRIPTION;
    const variables = {
      connectorId,
      description,
      updatedBy,
    };
    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response.updateConnectorDescription;
    } catch (error) {
      throw error;
    }
  }
  async getTags(connectorId: string) {
    const queries = GET_TAG;
    const variables = {
      connectorId,
    };
    try {
      const response = await this.graphqlQueryV2(queries, variables);
      return response.getTags;
    } catch (error) {
      throw error;
    }
  }
  async removeTag(connectorId: string, tagId: string, removedBy: string) {
    const mutation = REMOVE_TAG;
    const variables = {
      connectorId,
      tagId,
      removedBy,
    };
    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response.removeTag;
    } catch (error) {
      throw error;
    }
  }
  async searchTopic(
    filterTopicRequest: any,
    pageSize: number,
    currentPage: number
  ) {
    const query = SEARCH_TOPIC;
    const variables = {
      filterTopicRequest: filterTopicRequest,
      partnerId: this.orgId,
      pageSize,
      currentPage,
    };
    try {
      const response = await this.graphqlMutationV2(query, variables);
      return response.searchTopic;
    } catch (error) {
      throw error;
    }
  }
  async closeTopic(id: string, updatedBy: string) {
    const mutation = CLOSE_TOPIC;
    const variables = {
      id,
      updatedBy,
    };
    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response.closeTopic;
    } catch (error) {
      throw error;
    }
  }
  async createTopic(socialAppId: String, customerId: string, message: string) {
    const mutation = CREATE_TOPIC;
    const variables = {
      socialAppId,
      customerId,
      message,
    };
    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response.createTopic;
    } catch (error) {
      throw error;
    }
  }

  async getTopicByCustomerId(
    filterTopicRequest: any,
    pageSize: number,
    currentPage: number
  ) {
    const mutation = SEARCH_TOPIC;
    const variables = {
      filterTopicRequest,
      partnerId: this.orgId,
      pageSize,
      currentPage,
    };
    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response.searchTopic;
    } catch (error) {
      throw error;
    }
  }
  async getMyWorkEffortToday(performerId: string, source: string) {
    const query = GET_MY_WORK_EFFORT_TODAY;
    const variables = {
      partnerId: this.orgId,
      performerId,
      source,
    };
    try {
      const response = await this.graphqlQuery(query, variables);
      return response.getMyWorkEffortToDay;
    } catch (error) {
      throw error;
    }
  }
  async getMessages(topicId: String, pageSize: Number, pageNumber: Number) {
    const query = GET_MESSAGES;
    const variables = {
      topicId,
      pageSize,
      pageNumber,
    };
    try {
      const response = await this.graphqlQuery(query, variables);
      return response.getMessages;
    } catch (error) {
      throw error;
    }
  }
  async getResourceRelatedTopic(topicId: string, resourceType: string) {
    const query = GET_RESOURCE_RELATED_TOPIC;
    const variables = {
      topicId,
      resourceType,
    };
    try {
      const response = await this.graphqlQuery(query, variables);
      return response.getResourceRelatedTopic;
    } catch (error) {
      throw error;
    }
  }
  async addTopicRelatedResource(
    topicId: string,
    resourceId: string,
    resourceType: string,
    createdBy: string
  ) {
    const mutation = ADD_TOPIC_RELATED_RESOURCE;
    const variables = {
      topicId,
      resourceId,
      resourceType,
      createdBy,
    };
    try {
      const response = await this.graphqlMutation(mutation, variables)
      return response.addTopicRelatedResource
    } catch (error) {
      throw error
    }
  }
}
